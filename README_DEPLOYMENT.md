# Arkime CentOS 7.4 部署工具包

本工具包提供了在CentOS 7.4系统上部署Arkime的完整解决方案，支持在线和离线两种部署模式。

## 目标环境

- **服务器**: **************
- **系统**: CentOS 7.4
- **用户**: root/VictoR#.0.0
- **部署目录**: /home/<USER>/DataSource

## 文件说明

### 核心脚本

| 文件名 | 用途 | 使用场景 |
|--------|------|----------|
| `collect_offline_packages.sh` | 收集离线安装包 | 在有网络的系统上运行，收集所有依赖包 |
| `prepare_offline_deployment.sh` | 一键准备离线部署 | 自动化收集和打包离线安装包 |
| `deploy_arkime_centos7.sh` | 在线部署脚本 | 直接在目标服务器上在线安装 |
| `remote_deploy.sh` | 远程部署工具 | 从本地连接到目标服务器进行部署 |
| `test_deployment.sh` | 部署测试脚本 | 验证部署是否成功 |
| `download_npm_packages_offline.sh` | npm离线包下载 | 专门下载npm依赖的离线安装包 |
| `test_npm_offline.sh` | npm离线测试 | 验证npm离线安装功能 |

### 文档文件

| 文件名 | 内容 |
|--------|------|
| `ARKIME_DEPLOYMENT_GUIDE.md` | 详细部署指南 |
| `NPM_OFFLINE_GUIDE.md` | npm离线安装专门指南 |
| `README_DEPLOYMENT.md` | 本文件，工具包说明 |

### 修改的系统文件

| 文件名 | 修改内容 |
|--------|----------|
| `Makefile` | 修改install-exec-local和check-local，支持npm离线安装 |

## 快速开始

### 方式一：在线部署

```bash
# 1. 直接远程在线部署
chmod +x remote_deploy.sh
./remote_deploy.sh --mode online --server **************

# 2. 或者在目标服务器上直接运行
scp deploy_arkime_centos7.sh root@**************:/tmp/
ssh root@************** "chmod +x /tmp/deploy_arkime_centos7.sh && /tmp/deploy_arkime_centos7.sh"
```

### 方式二：离线部署（完整方案）

```bash
# 1. 准备完整离线安装包（在有网络的CentOS 7.4系统上）
chmod +x prepare_offline_deployment.sh
./prepare_offline_deployment.sh

# 2. 远程离线部署
./remote_deploy.sh --mode offline --server **************

# 3. 或者手动传输并安装
scp arkime_offline_packages.tar.gz root@**************:/home/<USER>/DataSource/
ssh root@**************
cd /home/<USER>/DataSource
tar -xzf arkime_offline_packages.tar.gz
cd arkime_offline_packages
./install_arkime_offline.sh
```

### 方式三：仅npm离线部署

```bash
# 1. 单独准备npm离线包
chmod +x download_npm_packages_offline.sh
./download_npm_packages_offline.sh

# 2. 将npm离线包放到项目根目录
# 这样Makefile会自动使用离线包进行安装

# 3. 正常编译安装
./configure --prefix=/opt/arkime
make
make install  # 会自动使用npm离线包
```

### 验证部署

```bash
# 测试部署结果
chmod +x test_deployment.sh
./test_deployment.sh
```

## 详细使用说明

### 1. 离线包收集 (`collect_offline_packages.sh`)

**用途**: 在有网络连接的CentOS 7.4系统上收集所有必需的安装包

**运行条件**:
- CentOS 7.4或兼容系统
- Root权限
- 网络连接
- 至少10GB可用磁盘空间

**使用方法**:
```bash
chmod +x collect_offline_packages.sh
./collect_offline_packages.sh
```

**输出**: 创建 `arkime_offline_packages` 目录，包含所有依赖包

### 2. 一键离线准备 (`prepare_offline_deployment.sh`)

**用途**: 自动化的离线包收集和打包工具

**特点**:
- 交互式界面
- 自动验证系统要求
- 生成压缩包
- 包含完整性检查

**使用方法**:
```bash
chmod +x prepare_offline_deployment.sh
./prepare_offline_deployment.sh
```

**输出**: `arkime_offline_packages.tar.gz` 压缩包

### 6. npm离线包下载 (`download_npm_packages_offline.sh`)

**用途**: 专门下载npm依赖的离线安装包

**特点**:
- 基于package-lock.json精确下载
- 生成生产和完整两种模式的包
- 包含tarball文件和安装脚本
- 支持多种安装方式

**使用方法**:
```bash
chmod +x download_npm_packages_offline.sh
./download_npm_packages_offline.sh
```

**输出**: `npm_offline_packages` 目录，包含完整的npm离线安装包

### 7. npm离线测试 (`test_npm_offline.sh`)

**用途**: 验证npm离线安装功能的完整性和正确性

**测试项目**:
- 离线包完整性检查
- 生产环境安装测试
- 完整安装测试
- 手动解压测试
- Makefile集成测试
- 性能测试

**使用方法**:
```bash
chmod +x test_npm_offline.sh
./test_npm_offline.sh
```

### 3. 在线部署 (`deploy_arkime_centos7.sh`)

**用途**: 在目标服务器上直接进行在线安装

**功能**:
- 系统环境配置
- 依赖包安装
- Arkime编译安装
- Elasticsearch安装配置
- 服务配置

**使用方法**:
```bash
# 在目标服务器上运行
chmod +x deploy_arkime_centos7.sh
./deploy_arkime_centos7.sh
```

### 4. 远程部署 (`remote_deploy.sh`)

**用途**: 从本地连接到目标服务器进行自动化部署

**支持模式**:
- 在线部署 (`--mode online`)
- 离线部署 (`--mode offline`)

**使用方法**:
```bash
# 在线部署
./remote_deploy.sh --mode online --server **************

# 离线部署
./remote_deploy.sh --mode offline --server **************

# 自定义参数
./remote_deploy.sh --mode online --server IP --user USER --password PASS --dir DIR
```

**参数说明**:
- `--mode`: 部署模式 (online/offline)
- `--server`: 目标服务器IP
- `--user`: SSH用户名
- `--password`: SSH密码
- `--dir`: 部署目录

### 5. 部署测试 (`test_deployment.sh`)

**用途**: 全面验证Arkime部署状态

**检查项目**:
- SSH连接
- 系统信息
- Arkime安装状态
- Node.js环境
- Elasticsearch状态
- 服务运行状态
- 网络配置
- Web界面访问

**使用方法**:
```bash
chmod +x test_deployment.sh
./test_deployment.sh
```

## 部署流程图

```
开始
  ↓
选择部署方式
  ↓
┌─────────────┬─────────────┐
│   在线部署   │   离线部署   │
└─────────────┴─────────────┘
  ↓                ↓
直接运行          收集离线包
deploy_arkime     ↓
_centos7.sh      打包传输
  ↓                ↓
安装完成          离线安装
  ↓                ↓
  └────────────────┘
         ↓
    运行测试脚本
         ↓
      部署完成
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查网络连接
   - 验证用户名密码
   - 确认SSH服务运行

2. **依赖包下载失败**
   - 检查网络连接
   - 更换软件源
   - 手动下载缺失包

3. **编译失败**
   - 检查系统版本兼容性
   - 确认开发工具安装
   - 查看编译日志

4. **服务启动失败**
   - 检查端口占用
   - 查看服务日志
   - 验证配置文件

### 日志位置

- Arkime日志: `/opt/arkime/logs/`
- Elasticsearch日志: `/var/log/elasticsearch/`
- 系统日志: `journalctl -u servicename`

## 性能建议

### 系统要求

- **最小配置**: 4核CPU, 8GB内存, 100GB存储
- **推荐配置**: 8核CPU, 16GB内存, 500GB存储
- **网络**: 千兆网卡

### 优化建议

1. **内存优化**: 根据系统内存调整Elasticsearch堆大小
2. **存储优化**: 使用SSD存储提高性能
3. **网络优化**: 调整网络缓冲区大小
4. **监控**: 定期监控系统资源使用情况

## 安全注意事项

1. **修改默认密码**: 安装完成后立即修改默认密码
2. **防火墙配置**: 只开放必要端口
3. **SSL配置**: 启用HTTPS访问
4. **访问控制**: 配置适当的用户权限
5. **定期更新**: 保持系统和软件最新

## 支持和反馈

如果在使用过程中遇到问题：

1. 查看详细部署指南: `ARKIME_DEPLOYMENT_GUIDE.md`
2. 运行测试脚本诊断问题
3. 检查相关日志文件
4. 参考官方文档: https://arkime.com/

## 版本信息

- **Arkime**: 最新版本
- **Node.js**: 20.19.2
- **Elasticsearch**: 7.17.15
- **目标系统**: CentOS 7.4
- **工具包版本**: 1.0

---

**注意**: 请在生产环境部署前，先在测试环境中验证所有功能。确保备份重要数据，并制定回滚计划。
