# Arkime npm离线安装指南

本指南说明如何为Arkime项目创建和使用npm离线安装包，支持在无网络环境中安装Node.js依赖。

## 概述

Arkime项目包含大量的npm依赖包，在内网或离线环境中部署时，需要预先下载所有依赖包。本方案提供了完整的npm离线安装解决方案。

## 文件说明

### 核心文件

1. **`download_npm_packages_offline.sh`** - npm离线包下载脚本
2. **修改后的`Makefile`** - 支持离线npm安装的构建文件
3. **`collect_offline_packages.sh`** - 集成了npm离线下载的完整离线包收集脚本

## 使用方法

### 方法一：单独下载npm离线包

```bash
# 1. 在有网络的环境中运行
chmod +x download_npm_packages_offline.sh
./download_npm_packages_offline.sh

# 2. 生成的离线包目录
ls npm_offline_packages/
# 输出:
# ├── .npm_cache/                 # npm缓存目录
# ├── tarballs/                   # 所有包的tarball文件
# ├── node_modules_production.tar.gz  # 生产环境依赖包
# ├── node_modules_full.tar.gz    # 完整依赖包
# ├── install_npm_offline.sh     # 离线安装脚本
# ├── .npmrc                      # npm配置文件
# ├── package.json               # 项目配置文件
# ├── package-lock.json          # 锁定版本文件
# └── README.md                   # 使用说明
```

### 方法二：集成到完整离线部署包

```bash
# 使用改进的离线包收集脚本
chmod +x collect_offline_packages.sh
./collect_offline_packages.sh

# 这会自动调用npm离线下载脚本，并将结果集成到完整的离线部署包中
```

## Makefile修改说明

修改后的Makefile支持以下三种npm安装模式：

### 1. 离线脚本模式（优先级最高）

```makefile
# 如果存在 npm_offline_packages/install_npm_offline.sh
# 使用离线安装脚本进行安装
npm_offline_packages/install_npm_offline.sh $(DESTDIR)/opt/arkime production
```

### 2. 离线压缩包模式

```makefile
# 如果存在 npm_offline_packages/node_modules_production.tar.gz
# 直接解压预打包的node_modules
tar -xzf npm_offline_packages/node_modules_production.tar.gz -C $(DESTDIR)/opt/arkime
```

### 3. 在线模式（回退）

```makefile
# 如果没有离线包，回退到传统的在线安装
npm ci
```

## 离线安装流程

### 准备阶段（有网络环境）

```bash
# 1. 下载npm离线包
./download_npm_packages_offline.sh

# 2. 将npm_offline_packages目录放到Arkime项目根目录
mv npm_offline_packages /path/to/arkime/

# 3. 打包整个项目
tar -czf arkime_with_npm_offline.tar.gz arkime/
```

### 部署阶段（离线环境）

```bash
# 1. 解压项目
tar -xzf arkime_with_npm_offline.tar.gz

# 2. 编译安装（会自动使用离线npm包）
cd arkime/
./configure --prefix=/opt/arkime
make
make install
```

## 手动使用离线npm包

如果需要手动安装npm依赖：

### 使用安装脚本

```bash
# 安装生产环境依赖到指定目录
./npm_offline_packages/install_npm_offline.sh /opt/arkime production

# 安装完整依赖（包含开发依赖）
./npm_offline_packages/install_npm_offline.sh /opt/arkime full
```

### 直接解压

```bash
# 解压生产环境依赖
tar -xzf npm_offline_packages/node_modules_production.tar.gz -C /opt/arkime

# 解压完整依赖
tar -xzf npm_offline_packages/node_modules_full.tar.gz -C /opt/arkime
```

### 使用npm离线安装

```bash
# 复制配置文件
cp npm_offline_packages/.npmrc /opt/arkime/
cp npm_offline_packages/package*.json /opt/arkime/

# 设置缓存目录
export NPM_CONFIG_CACHE=$(pwd)/npm_offline_packages/.npm_cache

# 离线安装
cd /opt/arkime
npm ci --production --offline
```

## 验证安装

```bash
# 检查node_modules目录
ls -la /opt/arkime/node_modules/

# 验证关键依赖包
ls /opt/arkime/node_modules/ | grep -E "(express|elasticsearch|vue)"

# 测试Node.js应用启动
cd /opt/arkime
node -e "console.log('Dependencies loaded successfully')"
```

## 故障排除

### 常见问题

#### 1. npm包下载失败

**症状**: 下载过程中出现网络错误
**解决方案**:
```bash
# 清理缓存重试
rm -rf npm_offline_packages/
npm cache clean --force
./download_npm_packages_offline.sh
```

#### 2. 离线安装失败

**症状**: make install时npm相关错误
**解决方案**:
```bash
# 检查npm_offline_packages目录是否存在
ls -la npm_offline_packages/

# 手动安装npm依赖
./npm_offline_packages/install_npm_offline.sh /opt/arkime production
```

#### 3. Node.js版本不匹配

**症状**: 某些包无法正常工作
**解决方案**:
```bash
# 确保Node.js版本一致
node --version  # 应该是20.19.2

# 重新下载适合当前Node.js版本的包
rm -rf npm_offline_packages/
./download_npm_packages_offline.sh
```

#### 4. 权限问题

**症状**: 安装时权限错误
**解决方案**:
```bash
# 修复权限
chmod -R 755 npm_offline_packages/
chmod +x npm_offline_packages/install_npm_offline.sh

# 以正确用户运行
sudo chown -R arkime:arkime /opt/arkime/node_modules/
```

## 高级配置

### 自定义npm配置

编辑 `npm_offline_packages/.npmrc` 文件：

```ini
# 自定义配置
cache=./npm_offline_packages/.npm_cache
prefer-offline=true
audit=false
fund=false
registry=https://registry.npmjs.org/
```

### 包大小优化

```bash
# 只下载生产环境依赖
npm ci --production --cache ./npm_offline_packages/.npm_cache

# 清理开发依赖
npm prune --production
```

### 多架构支持

```bash
# 为不同架构下载包
npm config set target_arch x64
npm config set target_platform linux
./download_npm_packages_offline.sh
```

## 性能优化

### 减少包大小

1. **移除不必要的文件**:
```bash
# 清理缓存中的临时文件
find npm_offline_packages/.npm_cache -name "*.log" -delete
find npm_offline_packages/.npm_cache -name "*.tmp" -delete
```

2. **压缩tarball文件**:
```bash
# 重新压缩以减少大小
cd npm_offline_packages/
tar -czf node_modules_production_compressed.tar.gz node_modules/
```

### 加速安装

1. **使用本地缓存**:
```bash
# 设置本地缓存目录
export NPM_CONFIG_CACHE=/tmp/npm_cache_local
```

2. **并行安装**:
```bash
# 增加并行安装数量
npm config set maxsockets 20
```

## 集成到CI/CD

### GitLab CI示例

```yaml
stages:
  - prepare
  - build
  - deploy

prepare_npm_offline:
  stage: prepare
  script:
    - ./download_npm_packages_offline.sh
  artifacts:
    paths:
      - npm_offline_packages/
    expire_in: 1 week

build_arkime:
  stage: build
  dependencies:
    - prepare_npm_offline
  script:
    - ./configure --prefix=/opt/arkime
    - make
    - make install
```

### Jenkins Pipeline示例

```groovy
pipeline {
    agent any
    stages {
        stage('Prepare NPM Offline') {
            steps {
                sh './download_npm_packages_offline.sh'
                archiveArtifacts artifacts: 'npm_offline_packages/**', fingerprint: true
            }
        }
        stage('Build Arkime') {
            steps {
                sh '''
                    ./configure --prefix=/opt/arkime
                    make
                    make install
                '''
            }
        }
    }
}
```

## 总结

通过本方案，您可以：

1. **完全离线部署** - 无需网络连接即可安装所有npm依赖
2. **自动化集成** - Makefile自动检测并使用离线包
3. **灵活配置** - 支持多种安装模式和自定义配置
4. **易于维护** - 清晰的目录结构和详细的使用说明

这个解决方案特别适合企业内网环境、安全要求较高的环境，以及网络条件不稳定的部署场景。
