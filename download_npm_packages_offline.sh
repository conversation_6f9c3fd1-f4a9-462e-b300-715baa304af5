#!/bin/bash
# 离线下载npm包脚本
# 基于package-lock.json下载所有依赖包

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OFFLINE_NPM_DIR="${SCRIPT_DIR}/npm_offline_packages"
CACHE_DIR="${OFFLINE_NPM_DIR}/.npm_cache"
TARBALLS_DIR="${OFFLINE_NPM_DIR}/tarballs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查必要文件
check_requirements() {
    log_info "检查必要文件..."
    
    if [[ ! -f "${SCRIPT_DIR}/package.json" ]]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    if [[ ! -f "${SCRIPT_DIR}/package-lock.json" ]]; then
        log_error "package-lock.json 文件不存在"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "必要文件检查完成"
}

# 创建目录结构
create_directories() {
    log_info "创建离线包目录结构..."
    
    mkdir -p "${OFFLINE_NPM_DIR}"
    mkdir -p "${CACHE_DIR}"
    mkdir -p "${TARBALLS_DIR}"
    
    log_success "目录结构创建完成"
}

# 下载npm包
download_npm_packages() {
    log_info "开始下载npm包..."
    
    # 复制package文件到临时目录
    local temp_dir="${OFFLINE_NPM_DIR}/temp_download"
    mkdir -p "$temp_dir"
    
    cp "${SCRIPT_DIR}/package.json" "$temp_dir/"
    cp "${SCRIPT_DIR}/package-lock.json" "$temp_dir/"
    
    cd "$temp_dir"
    
    # 配置npm使用本地缓存
    npm config set cache "${CACHE_DIR}"
    
    log_info "下载生产依赖包..."
    npm ci --production --cache "${CACHE_DIR}" --prefer-offline=false
    
    log_info "下载开发依赖包..."
    npm ci --cache "${CACHE_DIR}" --prefer-offline=false
    
    # 打包node_modules
    log_info "打包node_modules..."
    tar -czf "${OFFLINE_NPM_DIR}/node_modules_production.tar.gz" node_modules/
    
    # 重新安装包含开发依赖
    rm -rf node_modules/
    npm ci --cache "${CACHE_DIR}" --prefer-offline=false
    tar -czf "${OFFLINE_NPM_DIR}/node_modules_full.tar.gz" node_modules/
    
    cd "${SCRIPT_DIR}"
    rm -rf "$temp_dir"
    
    log_success "npm包下载完成"
}

# 提取tarball文件
extract_tarballs() {
    log_info "提取tarball文件..."
    
    # 从缓存中复制所有tarball文件
    find "${CACHE_DIR}" -name "*.tgz" -exec cp {} "${TARBALLS_DIR}/" \;
    
    local tarball_count=$(find "${TARBALLS_DIR}" -name "*.tgz" | wc -l)
    log_info "提取了 ${tarball_count} 个tarball文件"
    
    log_success "tarball文件提取完成"
}

# 创建离线安装脚本
create_offline_install_script() {
    log_info "创建离线npm安装脚本..."
    
    cat > "${OFFLINE_NPM_DIR}/install_npm_offline.sh" << 'EOF'
#!/bin/bash
# npm离线安装脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="${1:-$(pwd)}"
MODE="${2:-production}"  # production 或 full

log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查参数
if [[ "$MODE" != "production" && "$MODE" != "full" ]]; then
    log_error "模式参数错误，只支持 'production' 或 'full'"
    exit 1
fi

log_info "开始离线安装npm包到: $INSTALL_DIR"
log_info "安装模式: $MODE"

# 创建本地npm缓存
local_cache_dir="${INSTALL_DIR}/.npm_cache_local"
mkdir -p "$local_cache_dir"

# 复制tarball文件到本地缓存
log_info "设置本地npm缓存..."
cp -r "${SCRIPT_DIR}/tarballs/"* "$local_cache_dir/" 2>/dev/null || true

# 配置npm使用本地缓存
npm config set cache "$local_cache_dir"

# 根据模式选择安装包
if [[ "$MODE" == "production" ]]; then
    if [[ -f "${SCRIPT_DIR}/node_modules_production.tar.gz" ]]; then
        log_info "解压生产环境node_modules..."
        tar -xzf "${SCRIPT_DIR}/node_modules_production.tar.gz" -C "$INSTALL_DIR"
    else
        log_error "生产环境包文件不存在"
        exit 1
    fi
else
    if [[ -f "${SCRIPT_DIR}/node_modules_full.tar.gz" ]]; then
        log_info "解压完整node_modules..."
        tar -xzf "${SCRIPT_DIR}/node_modules_full.tar.gz" -C "$INSTALL_DIR"
    else
        log_error "完整包文件不存在"
        exit 1
    fi
fi

# 设置权限
if [[ -d "${INSTALL_DIR}/node_modules" ]]; then
    chmod -R 755 "${INSTALL_DIR}/node_modules"
fi

log_success "npm离线安装完成"
EOF
    
    chmod +x "${OFFLINE_NPM_DIR}/install_npm_offline.sh"
    
    log_success "离线安装脚本创建完成"
}

# 创建npm配置文件
create_npm_config() {
    log_info "创建npm离线配置..."
    
    # 创建.npmrc文件用于离线安装
    cat > "${OFFLINE_NPM_DIR}/.npmrc" << EOF
# npm离线配置
cache=${OFFLINE_NPM_DIR}/.npm_cache
prefer-offline=true
audit=false
fund=false
EOF
    
    # 复制package文件
    cp "${SCRIPT_DIR}/package.json" "${OFFLINE_NPM_DIR}/"
    cp "${SCRIPT_DIR}/package-lock.json" "${OFFLINE_NPM_DIR}/"
    
    log_success "npm配置文件创建完成"
}

# 生成使用说明
create_usage_guide() {
    log_info "生成使用说明..."
    
    cat > "${OFFLINE_NPM_DIR}/README.md" << 'EOF'
# npm离线安装包

本目录包含Arkime项目的所有npm依赖包，支持离线安装。

## 目录结构

```
npm_offline_packages/
├── .npm_cache/                 # npm缓存目录
├── tarballs/                   # 所有包的tarball文件
├── node_modules_production.tar.gz  # 生产环境依赖包
├── node_modules_full.tar.gz    # 完整依赖包（包含开发依赖）
├── install_npm_offline.sh     # 离线安装脚本
├── .npmrc                      # npm配置文件
├── package.json               # 项目配置文件
├── package-lock.json          # 锁定版本文件
└── README.md                   # 本文件
```

## 使用方法

### 方法1: 使用安装脚本（推荐）

```bash
# 安装生产环境依赖
./install_npm_offline.sh /path/to/install production

# 安装完整依赖（包含开发依赖）
./install_npm_offline.sh /path/to/install full
```

### 方法2: 手动解压

```bash
# 解压到目标目录
tar -xzf node_modules_production.tar.gz -C /path/to/install
```

### 方法3: 使用npm离线安装

```bash
# 复制配置文件
cp .npmrc package*.json /path/to/install/

# 设置缓存目录
export NPM_CONFIG_CACHE=$(pwd)/.npm_cache

# 离线安装
cd /path/to/install
npm ci --production --offline
```

## 注意事项

1. 确保目标系统有相同的Node.js版本
2. 某些包可能需要编译，确保有必要的编译工具
3. 如果安装失败，检查系统架构是否匹配

EOF
    
    log_success "使用说明生成完成"
}

# 主函数
main() {
    log_info "开始创建npm离线安装包..."
    
    check_requirements
    create_directories
    download_npm_packages
    extract_tarballs
    create_offline_install_script
    create_npm_config
    create_usage_guide
    
    # 显示统计信息
    local package_size=$(du -sh "${OFFLINE_NPM_DIR}" | cut -f1)
    local tarball_count=$(find "${TARBALLS_DIR}" -name "*.tgz" | wc -l)
    
    log_success "npm离线安装包创建完成!"
    echo ""
    echo "=== 统计信息 ==="
    echo "包大小: $package_size"
    echo "tarball数量: $tarball_count"
    echo "位置: $OFFLINE_NPM_DIR"
    echo ""
    echo "=== 使用方法 ==="
    echo "1. 将整个 npm_offline_packages 目录复制到目标服务器"
    echo "2. 运行: ./npm_offline_packages/install_npm_offline.sh /opt/arkime production"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
